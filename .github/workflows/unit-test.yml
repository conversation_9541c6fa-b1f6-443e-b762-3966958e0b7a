name: Unit Tests

on:
  pull_request:
  push:
    branches:
      - main

jobs:
  test:
    if: github.repository == 'bytedance/trae-agent'
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.12'

    - name: Install uv
      uses: astral-sh/setup-uv@v6

    - name: Create virtual environment and install dependencies
      run: |
        uv sync --all-extras

    - name: Run unit tests
      env:
        SKIP_OLLAMA_TEST: true
        SKIP_OPENROUTER_TEST: true
      run: |
        uv run pytest tests/ -v --tb=short --continue-on-collection-errors
